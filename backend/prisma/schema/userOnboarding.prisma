enum OnboardingStep {
  PHONE_VERIFICATION
  LANGUAGE_UPDATE
  PROFILE_SETUP
  VEHICLE_DOCUMENTS_VERIFICATION
  DOCUMENT_UPLOAD
  PROFILE_PHOTO_UPLOAD
  KYC_DOCUMENT_UPLOAD
  VEHICLE_REGISTRATION
  BACKGROUND_CHECK
  TRAINING_COMPLETION
  ACCOUNT_ACTIVATION
  COMPLETED

  @@map("onboarding_step")
}

model UserOnboarding {
  id           String         @id @default(uuid()) @map("id") @db.Uuid
  userId       String         @map("user_id") @db.Uuid
  roleId       String         @map("role_id") @db.Uuid
  currentStep  OnboardingStep @map("current_step")
  lastActiveAt DateTime?      @map("last_active_at")
  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")
  deletedAt    DateTime?      @map("deleted_at") @db.Timestamptz

  // Relations
  user User @relation(fields: [userId], references: [id])
  role Role @relation(fields: [roleId], references: [id])

  @@map("user_onboarding")
}
