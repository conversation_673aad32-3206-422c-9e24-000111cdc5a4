import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { CreateTakeoffResponse, ListTakeoffResponse } from '../types/takeoff';
import { Driver, ListDriverResponse } from '../types/driver';

export interface ListTakeoffParams {
  page: number;
  perPage: number;
  search?: string;
  status?: 'submitted' | 'won' | 'lost';
  orderBySubmissionDate?: 'asc' | 'desc';
}

export const useListTakeoff = ({
  page,
  perPage,
  search,
  status,
  orderBySubmissionDate,
}: ListTakeoffParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: ['takeoff', page, perPage, search, status, orderBySubmissionDate],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListTakeoffResponse> => {
      return apiClient.get('/takeoff', {
        params: {
          page,
          perPage,
          search,
          status,
          orderBySubmissionDate,
        },
      });
    },
  });
};

export const useGetTakeoff = (id: number | null) => {
  return useQuery({
    queryKey: ['takeoff', id],
    queryFn: (): Promise<CreateTakeoffResponse> => {
      if (!id) throw new Error('Takeoff ID is required');
      return apiClient.get(`/takeoff/${id}`);
    },
    enabled: !!id, // Only run the query if id is provided
  });
};

export interface ListDriverParams {
   page: number;
   perPage: number;
   search?: string;
   status?: 'pending' | 'active' | 'inactive';
   location?: string;
}

export const useListDriver = ({ page, perPage, search, status, location }: ListDriverParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['driver', page, perPage, search, status, location],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListDriverResponse> => {
         return apiClient.get('/driver', {
            params: {
               page,
               perPage,
               search,
               status,
               location,
            },
         });
      },
   });
};

export const useGetDriver = (id: number | null) => {
   return useQuery({
      queryKey: ['driver', id],
      queryFn: (): Promise<Driver> => {
         if (!id) throw new Error('Driver ID is required');
         return apiClient.get(`/driver/${id}`);
      },
      enabled: !!id, // Only run the query if id is provided
   });
};
