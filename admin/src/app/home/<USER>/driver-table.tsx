'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Check, RefreshCw, X, Eye, Edit, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { useDeleteDriver } from '../api/mutations';
import { Driver, ListDriverResponse } from '../types/driver';
import { DeleteDriverDialog } from './delete-driver-dialog';
import { EditDriver } from './edit-driver';
import { DriverTableEmpty } from './driver-table-empty';
import { DriverTableFilteredEmpty } from './driver-table-filtered-empty';
import { DriverTableLoading } from './driver-table-loading';
import { CustomPagination } from '@/components/pagination';

// Define the columns for the table
const getColumns = ({
  deleteDriverMutation,
  handleDeleteClick,
  handleEditClick,
  handleViewClick,
  driverToDelete,
}: {
  handleDeleteClick: (id: number) => void;
  handleEditClick: (id: number) => void;
  handleViewClick: (id: number) => void;
  deleteDriverMutation: any;
  driverToDelete: number | null;
}): ColumnDef<Driver>[] => [
  {
    accessorKey: 'name',
    header: () => <div className="text-left">Driver</div>,
    cell: ({ row }) => {
      const driver = row.original as Driver;
      return (
        <div className="text-left">
          <div className="font-medium">{driver.firstName} {driver.lastName}</div>
          <div className="text-sm text-gray-500">{driver.driverId}</div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'contact',
    header: () => <div className="text-left">Contact</div>,
    cell: ({ row }) => {
      const driver = row.original as Driver;
      return (
        <div className="text-left">
          <div className="font-medium">{driver.phoneNumber}</div>
          <div className="text-sm text-gray-500">{driver.email}</div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'location',
    header: () => <div className="text-left">Location</div>,
    cell: ({ row }) => {
      const driver = row.original as Driver;
      return (
        <div className="text-left">
          <div className="font-medium">{driver.location}</div>
        </div>
      );
    },
    size: 150,
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return (
        <div className="text-left">
          {status === 'pending' ? (
            <Badge
              variant="outline"
              className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1 w-fit"
            >
              <RefreshCw className="h-3 w-3" />
              Pending Review
            </Badge>
          ) : status === 'active' ? (
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1 w-fit"
            >
              <Check className="h-3 w-3" />
              Active
            </Badge>
          ) : status === 'inactive' ? (
            <Badge
              variant="outline"
              className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1 w-fit"
            >
              <X className="h-3 w-3" />
              Inactive
            </Badge>
          ) : (
            <span>{status}</span>
          )}
        </div>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'progress',
    header: () => <div className="text-left">Progress</div>,
    cell: ({ row }) => {
      const driver = row.original as Driver;
      const progress = driver.progress || 0;
      return (
        <div className="text-left">
          <div className="flex items-center gap-2">
            <div className="w-16 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium">{progress}%</span>
          </div>
        </div>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'joinedDate',
    header: () => <div className="text-left">Joined</div>,
    cell: ({ row }) => {
      const joinedDate = row.getValue('joinedDate');
      if (!joinedDate) return <div className="text-left">-</div>;

      const date = new Date(joinedDate as string);
      return (
        <div className="text-left">
          {date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          })}
        </div>
      );
    },
    size: 100,
  },
  {
    id: 'actions',
    header: () => <div className="text-center">Actions</div>,
    cell: ({ row }) => {
      const driver = row.original as Driver;
      return (
        <div className="flex justify-center gap-1">
          <Button
            variant="outline"
            size="sm"
            className="text-gray-700 border-gray-300 h-8 px-2"
            onClick={() => handleViewClick(driver.id)}
          >
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-blue-700 border-blue-300 h-8 px-2"
            onClick={() => handleEditClick(driver.id)}
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-red-700 border-red-300 h-8 px-2"
            onClick={() => handleDeleteClick(driver.id)}
            disabled={deleteDriverMutation.isPending && driverToDelete === driver.id}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      );
    },
    size: 200,
  },
];

interface DriverTableProps {
  data: ListDriverResponse | undefined;
  isLoading: boolean;
  currentPage: number;
  onPageChange: (page: number) => void;
  hasFilters: boolean;
  hasSearch: boolean;
  hasStatus: boolean;
  hasLocation: boolean;
  onClearFilters: () => void;
  onRefresh: () => void;
}

export function DriverTable({
  data,
  isLoading,
  currentPage,
  onPageChange,
  hasFilters,
  hasSearch,
  hasStatus,
  hasLocation,
  onClearFilters,
  onRefresh,
}: DriverTableProps) {
  const [driverToDelete, setDriverToDelete] = useState<number | null>(null);
  const deleteDriverMutation = useDeleteDriver();
  const queryClient = useQueryClient();

  const handleDeleteClick = (id: number) => {
    setDriverToDelete(id);
  };

  const handleEditClick = (id: number) => {
    // Handle edit logic
    console.log('Edit driver:', id);
  };

  const handleViewClick = (id: number) => {
    // Handle view logic
    console.log('View driver:', id);
  };

  const handleDeleteConfirm = () => {
    if (!driverToDelete) return;

    deleteDriverMutation.mutate(driverToDelete, {
      onSuccess: () => {
        toast.success('Driver deleted successfully');
        setDriverToDelete(null);
        queryClient.invalidateQueries({ queryKey: ['driver'] });
      },
      onError: (error) => {
        toast.error(error);
        setDriverToDelete(null);
      },
    });
  };

  const columns = getColumns({
    deleteDriverMutation,
    handleDeleteClick,
    handleEditClick,
    handleViewClick,
    driverToDelete,
  });

  const table = useReactTable({
    data: data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Loading state
  if (isLoading) {
    return <DriverTableLoading />;
  }

  // Empty state with filters
  if (!data?.data?.length && hasFilters) {
    return (
      <DriverTableFilteredEmpty
        hasSearch={hasSearch}
        hasStatus={hasStatus}
        hasLocation={hasLocation}
        onClearFilters={onClearFilters}
      />
    );
  }

  // Empty state without filters
  if (!data?.data?.length) {
    return <DriverTableEmpty />;
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className="border-b bg-gray-50/50">
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className="h-12 px-4 text-left align-middle font-medium text-gray-900"
                      style={{ width: header.getSize() }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className="border-b transition-colors hover:bg-gray-50/50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="p-4 align-middle">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {data && data.totalPages > 1 && (
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.totalPages}
          onPageChange={onPageChange}
          hasNext={data.hasNextPage}
          hasPrev={data.hasPrevPage}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteDriverDialog
        isOpen={!!driverToDelete}
        onClose={() => setDriverToDelete(null)}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteDriverMutation.isPending}
      />
    </div>
  );
}
