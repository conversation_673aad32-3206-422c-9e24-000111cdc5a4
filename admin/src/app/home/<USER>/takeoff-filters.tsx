'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { ArrowDownAZ, ArrowUpAZ, Search, X } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';

export interface TakeoffFiltersProps {
  onSearchChange: (search: string) => void;
  onStatusChange: (status: string | undefined) => void;
  onSortChange: (sort: 'asc' | 'desc' | undefined) => void;
  search: string;
  status: string | undefined;
  sort: 'asc' | 'desc' | undefined;
}

// isLoading is not used in this component but passed from parent
export function TakeoffFilters({
  onSearchChange,
  onStatusChange,
  onSortChange,
  search,
  status,
  sort,
  isLoading,
}: TakeoffFiltersProps & { isLoading?: boolean }) {
  const [searchValue, setSearchValue] = useState(search || '');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local search state when prop changes
  useEffect(() => {
    setSearchValue(search || '');
  }, [search]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search input with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Show searching indicator
    setIsSearching(true);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set a new timeout
    searchTimeoutRef.current = setTimeout(() => {
      onSearchChange(value);
      searchTimeoutRef.current = null;
      setIsSearching(false);
    }, 500); // 500ms debounce time
  };

  // Clear all filters
  const handleClearFilters = () => {
    // Clear any pending search timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setIsSearching(false);
    setSearchValue('');
    onSearchChange('');
    onStatusChange(undefined);
    onSortChange(undefined);
  };

  // Check if any filters are active
  const hasActiveFilters = !!search || !!status || !!sort;

  return (
    <div className="flex flex-col space-y-4 mb-4">
      <div className="flex flex-wrap gap-2 items-center">
        {/* Search Input */}
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search takeoffs..."
            value={searchValue}
            onChange={handleSearchChange}
            className="pl-8"
          />
          {(isSearching || (isLoading && searchValue)) && (
            <div className="absolute right-2.5 top-2.5 text-gray-500">
              <Spinner className="h-4 w-4 text-primary" />
            </div>
          )}
          {searchValue && !isSearching && !isLoading && (
            <button
              onClick={() => {
                // Clear any pending search timeout
                if (searchTimeoutRef.current) {
                  clearTimeout(searchTimeoutRef.current);
                  searchTimeoutRef.current = null;
                }
                setIsSearching(false);
                setSearchValue('');
                onSearchChange('');
              }}
              className="absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Status Filter */}
        <Select
          value={status || 'all'}
          onValueChange={(value) =>
            onStatusChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="submitted">Submitted</SelectItem>
            <SelectItem value="won">Won</SelectItem>
            <SelectItem value="lost">Lost</SelectItem>
          </SelectContent>
        </Select>

        {/* Sort by Submission Date */}
        <Select
          value={sort || 'default'}
          onValueChange={(value) =>
            onSortChange(
              value === 'default' ? undefined : (value as 'asc' | 'desc'),
            )
          }
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by Date" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Default Order</SelectItem>
            <SelectItem value="asc">
              <div className="flex items-center gap-2">
                <ArrowUpAZ className="h-4 w-4" />
                <span>Oldest First</span>
              </div>
            </SelectItem>
            <SelectItem value="desc">
              <div className="flex items-center gap-2">
                <ArrowDownAZ className="h-4 w-4" />
                <span>Newest First</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearFilters}
            className="text-gray-700 border-gray-300"
          >
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}
