'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Car, Plus } from 'lucide-react';

interface DriverTableEmptyProps {
  onCreateClick?: () => void;
}

export function DriverTableEmpty({ onCreateClick }: DriverTableEmptyProps) {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
        <Car className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No drivers found</h3>
      <p className="text-gray-500 text-center mb-6 max-w-md">
        Get started by adding your first driver to the system. You can manage their information, 
        track their progress, and monitor their status.
      </p>
      {onCreateClick && (
        <Button onClick={onCreateClick} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Add First Driver
        </Button>
      )}
    </div>
  );
}
