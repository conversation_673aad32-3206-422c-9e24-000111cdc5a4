'use client';

import { useLogout } from '@/api/logout';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { resetStore } from '@/store/store-helpers';
import { LogOut, User } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface UserDropdownMenuProps {
  userInitial?: string;
  avatarUrl?: string;
}

export function UserDropdownMenu({
  userInitial = 'U',
  avatarUrl,
}: UserDropdownMenuProps) {
  const logout = useLogout();
  const router = useRouter();

  const handleLogout = () => {
    logout.mutate();
    resetStore();
    router.push('/');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="h-8 w-8 cursor-pointer">
          {avatarUrl ? <AvatarImage src={avatarUrl} alt="User" /> : null}
          <AvatarFallback>{userInitial}</AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>
          <User className="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
