'use client';

import { Button } from '@/components/ui/button';
import { FileText, Plus, Search, X } from 'lucide-react';

interface TakeoffTableFilteredEmptyProps {
  hasSearch: boolean;
  hasStatus: boolean;
  hasSort: boolean;
  onClearFilters: () => void;
}

export function TakeoffTableFilteredEmpty({
  hasSearch,
  hasStatus,
  hasSort,
  onClearFilters,
}: TakeoffTableFilteredEmptyProps) {
  // Determine the message based on which filters are active
  const getMessage = () => {
    if (hasSearch && !hasStatus && !hasSort) {
      return 'No takeoffs match your search criteria';
    } else if (hasStatus && !hasSearch && !hasSort) {
      return 'No takeoffs with this status';
    } else if (hasSort && !hasSearch && !hasStatus) {
      return 'No takeoffs to sort';
    } else {
      return 'No takeoffs match your filters';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 mb-4">
        {hasSearch ? (
          <Search className="h-8 w-8 text-gray-500" />
        ) : (
          <FileText className="h-8 w-8 text-gray-500" />
        )}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-1">{getMessage()}</h3>
      <p className="text-sm text-gray-500 mb-6 text-center max-w-md">
        Try adjusting your filters or create a new takeoff
      </p>
      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onClearFilters}
          className="border-gray-300"
        >
          <X className="h-4 w-4" />
          Clear Filters
        </Button>
        <Button
          id="filtered-empty-create-takeoff-button"
          onClick={() => {
            // Find the "New Takeoff" button by ID and click it
            const newTakeoffButton = document.getElementById(
              'create-takeoff-trigger',
            );
            if (newTakeoffButton) {
              (newTakeoffButton as HTMLButtonElement).click();
            }
          }}
        >
          <Plus className="h-4 w-4" />
          Create Takeoff
        </Button>
      </div>
    </div>
  );
}
