'use client';

import { Card } from '@/components/ui/card';

import { CreateTakeoff } from '../components/create-takeoff';
import { useListTakeoff } from '../api/queries';
import { useState, useEffect, useRef } from 'react';
import { TakeoffTable } from '../components/takeoff-table';
import { UserDropdownMenu } from '../components/user-dropdown-menu';
import { useGetMe } from '@/api/get-me';
import { TakeoffFilters } from '../components/takeoff-filters';
import { LogoWithName } from '@/components/logo-name';

export function HomePage() {
  const [page, setPage] = useState(1);
  const [perPage] = useState(10);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [orderBySubmissionDate, setOrderBySubmissionDate] = useState<
    'asc' | 'desc' | undefined
  >(undefined);

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handleStatusChange = (value: string | undefined) => {
    setStatus(value);
    setPage(1);
  };

  const handleSortChange = (value: 'asc' | 'desc' | undefined) => {
    setOrderBySubmissionDate(value);
    setPage(1);
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    setSearch('');
    setStatus(undefined);
    setOrderBySubmissionDate(undefined);
    setPage(1);
  };

  const listTakeoff = useListTakeoff({
    page,
    perPage,
    search: search || undefined,
    status: status as 'submitted' | 'won' | 'lost' | undefined,
    orderBySubmissionDate,
  });

  // Ref to store the interval ID for cleanup
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // State to track if auto-refresh is active
  const [isAutoRefreshActive, setIsAutoRefreshActive] = useState(false);

  // State to track countdown timer
  const [countdown, setCountdown] = useState(10);

  // Ref to store the countdown interval ID
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-refetch logic for processing takeoffs
  useEffect(() => {
    // Clear any existing intervals
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    setIsAutoRefreshActive(false);

    // Check if there are any takeoffs in processing state
    const hasProcessingTakeoffs = listTakeoff.data?.data?.some(
      (takeoff) => takeoff.conversionStatus === 'processing',
    );

    // Only set up interval if:
    // 1. We have data
    // 2. There are processing takeoffs
    // 3. We're not currently loading (to avoid conflicts)
    if (
      hasProcessingTakeoffs &&
      !listTakeoff.isLoading &&
      !listTakeoff.isFetching
    ) {
      setIsAutoRefreshActive(true);
      setCountdown(10); // Reset countdown

      // Set up countdown timer (updates every second)
      countdownIntervalRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            return 10; // Reset to 10 when it reaches 1
          }
          return prev - 1;
        });
      }, 1000); // 1 second

      // Set up refetch timer (every 10 seconds)
      intervalRef.current = setInterval(() => {
        // Only refetch if we're not already fetching to avoid multiple concurrent requests
        if (!listTakeoff.isFetching) {
          listTakeoff.refetch();
          setCountdown(10); // Reset countdown after refetch
        }
      }, 10000); // 10 seconds
    } else {
      setIsAutoRefreshActive(false);
      setCountdown(10);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
      setIsAutoRefreshActive(false);
    };
  }, [
    listTakeoff.data?.data,
    listTakeoff.isLoading,
    listTakeoff.isFetching,
    listTakeoff.refetch,
  ]);

  // Function to refresh the takeoff list data
  const handleRefreshData = () => {
    listTakeoff.refetch();
  };

  // Cleanup interval on component unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
      setIsAutoRefreshActive(false);
    };
  }, []);

  const me = useGetMe();
  const userInitial = me.data?.fullName?.split(' ')[0][0] || 'X';

  return (
    <div className="min-h-screen bg-white w-[95%] mx-auto ">
      {/* Header */}
      <header className="border-b border-gray-200">
        <div className="pr-4 py-4 flex justify-between items-center">
          <LogoWithName className="scale-[80%] relative right-1" />
          <UserDropdownMenu userInitial={userInitial} />
        </div>
      </header>

      {/* Main Content */}
      <main className="px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-900">Takeoffs</h2>
          <CreateTakeoff />
        </div>

        <Card className="overflow-hidden py-4 px-4 rounded-sm">
          <TakeoffFilters
            search={search}
            status={status}
            sort={orderBySubmissionDate}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onSortChange={handleSortChange}
            isLoading={listTakeoff.isFetching && !listTakeoff.isLoading}
          />

          <TakeoffTable
            data={listTakeoff.data}
            isLoading={listTakeoff.isLoading}
            currentPage={page}
            onPageChange={(newPage) => setPage(newPage)}
            hasFilters={!!search || !!status || !!orderBySubmissionDate}
            hasSearch={!!search}
            hasStatus={!!status}
            hasSort={!!orderBySubmissionDate}
            onClearFilters={handleClearFilters}
            onRefresh={handleRefreshData}
            isAutoRefreshActive={isAutoRefreshActive}
            countdown={countdown}
          />
        </Card>
      </main>
    </div>
  );
}
